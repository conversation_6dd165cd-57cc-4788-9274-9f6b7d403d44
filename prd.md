### **产品需求文档 (PRD): 「沉淀」MVP**

**产品名称:** 沉淀 (Precipitation)
**版本:** 4.0 (最终发布版)
**日期:** 2025年6月10日
**产品负责人:** (您的名字)

---

#### **1. 产品愿景与使命 (Vision & Mission)**

*   **愿景:** 在信息爆炸的洪流中，为深度思考者提供一个宁静、高效的知识内化空间。
*   **使命:** 通过AI赋能，将繁杂的信息源（网页、文本）自动提炼为结构化的知识摘要，无缝连接原文与洞察，让知识的获取、理解和沉淀过程合而为一。

---

#### **2. 解决的问题与目标用户 (Problem & Target Audience)**

*   **问题陈述:**
    1.  **信息过载:** 现代知识工作者每天面对大量文章和报告，难以快速抓住核心。
    2.  **笔记割裂:** 使用传统工具手动做笔记耗时耗力，且笔记与原文脱节，难以回溯和查证。
    3.  **知识孤岛:** 笔记分散存储，难以形成体系，更无法被高效地检索和调用。
*   **目标用户画像 (Persona):**
    *   **姓名:** 李静
    *   **职业:** 32岁，互联网行业研究员/产品经理
    *   **行为特征:** 阅读量大，有做笔记的习惯，是Notion/Obsidian等工具的用户。对AI工具有浓厚兴趣，乐于尝试新工具提升效率。
    *   **核心痛点:** “我经常花半小时读完一篇行业报告，再花一小时用AI总结、整理笔记，但这些笔记和原文是分开的。当我回顾笔记里的某个观点时，想找原文的出处和上下文非常痛苦。”

---
#### **4. 功能范围与需求详述 (Functional Requirements)**

**史诗 1: 核心阅读与分析工作台 (The Core Reading & Analysis Workbench)**

> **核心用户故事:** 作为一个用户，我希望能在一个界面里，左边看原文，右边看AI自动生成的笔记，并且可以方便地把满意的笔记存起来。

| 需求ID | 需求描述 | 优先级 | 技术实现细节 |
| :--- | :--- | :--- | :--- |
| **F-1.1** | **统一输入入口** | **Must Have** | 提供一个简洁的输入框，支持粘贴纯文本和网页URL。系统能自动识别类型。 |
| **F-1.2** | **网页正文抓取** | **Must Have** | **后端:** 使用`@mozilla/readability`优先处理，对JS渲染的网站，使用`Playwright`作为后备方案。 |
| **F-1.3** | **AI笔记生成** | **Must Have** | **后端:** 调用LLM（如GPT-4o），使用精心设计的Prompt，将原文处理成包含固定结构（摘要、大纲、要点等）的Markdown。 |
| **F-1.4** | **两栏式沉浸布局** | **Must Have** | **前端:** 左侧为原文区域，右侧为AI笔记区域。左右比例约为6:4。 |
| **F-1.5** | **原文区域渲染** | **Must Have** | **前端:** 使用`@tailwindcss/typography`插件，确保无论是网页内容还是纯文本，都有优美的、统一的排版。 |
| **F-1.6** | **AI笔记区域渲染** | **Must Have** | **前端:** 使用Markdown渲染器展示AI笔记。标题、列表、引用等格式清晰。 |
| **F-1.7** | **上下文AI问答窗口**| **Must Have** | **前后端:** 在AI笔记区域下方提供一个聊天窗口。用户提问时，将“原文”和“AI笔记”作为上下文，调用LLM回答。 |
| **F-1.8** | **笔记到原文的跳转** | **Should Have**| **前后端:** 后端生成笔记时，需建立大纲标题与原文位置的映射。前端实现点击大纲标题，左侧原文自动滚动到对应位置。 |

**史诗 2: 知识入库与调用 (Archiving & Recalling)**

> **核心用户故事:** 作为一个用户，我希望能把我保存的所有笔记都同步到一个地方，然后像和一个专家聊天一样，随时向我的知识库提问。

| 需求ID | 需求描述 | 优先级 | 技术实现要点 |
| :--- | :--- | :--- | :--- |
| **F-2.1** | **本地数据库构建** | **Must Have** | **数据库:** 使用SQLite + Prisma。设计`Note`表，字段包含`id`, `title`(从原文或URL生成), `sourceType`, `sourceData`, `originalContent`, `aiNoteMarkdown`, `createdAt`。 |
| **F-2.2** | **“存入知识库”功能** | **Must Have** | **前后端:** 提供清晰的“存入知识库”按钮。点击后，将当前会话的原文和AI笔记，作为一个整体记录，存入本地SQLite。 |
| **F-2.3** | **知识库列表** | **Should Have**| **前后端:** 提供一个入口，可以查看所有已存入的笔记列表，并能点击加载历史记录。 |
| **F-2.4** | **与FastGPT的同步** | **Must Have** | **后端:** 创建同步API，查询本地所有笔记，格式化后分批推送至指定的FastGPT知识库。 |
| **F-2.5** | **手动同步触发器** | **Must Have** | **前端:** UI上提供“同步”按钮，并有明确的加载和成功/失败状态反馈。 |
| **F-2.6** | **FastGPT对话集成**| **Must Have** | **前端:** 提供一个“知识库对话”入口，跳转到新页面，使用`iframe`嵌入FastGPT的对话窗口。 |

---

#### **5. UI/UX 设计与产品文案 (Design & Copy)**

*   **设计原则:** 简洁、专注、高效。遵循“Calm Tech”理念，采用中性色调，辅以清晰的主色调作为引导。
*   **核心UI文案:**
    *   **初始页按钮:** 「生成笔记」
    *   **处理中提示:** 「正在生成AI笔记...」
    *   **AI输出物标题:** 「AI 笔记」
    *   **保存按钮:** 「存入知识库」
    *   **历史列表标题:** 「知识库」
    *   **同步按钮:** 「同步知识库」
    *   **对话页标题:** 「与知识库对话」

---

#### **6. 非功能性需求 (Non-Functional Requirements)**

*   **性能:** 90%的请求（中等长度文章）应在10秒内完成AI笔记的生成和呈现。
*   **兼容性:** 完美支持最新版的 Chrome, Safari, Firefox 浏览器。
*   **易用性:** 新用户无需阅读任何教程，即可凭直觉完成首次“生成笔记”并“存入知识库”的操作。

---

#### **7. MVP范围之外 (Out of Scope for MVP)**

*   **复杂模态支持:** 不支持PDF、图片(OCR)、音视频文件。
*   **高级交互:** 知识图谱可视化、笔记双向链接。
*   **用户系统:** 不做复杂的登录注册，MVP阶段可使用本地存储或简化认证。
*   **协同与分享:** 不支持多人协同编辑或公开分享。
*   **原生客户端:** 不开发移动端或桌面端原生应用。

---

#### **8. 上线标准 (Release Criteria)**

1.  所有标记为 **Must Have** 的功能需求已100%开发完成并通过测试。
2.  核心用户流程（输入->生成->保存->同步->对话）已端到端测试通过，无阻塞性BUG。
3.  UI界面与设计稿基本一致，在目标浏览器上无明显布局错位。
4.  应用已成功部署，公网可访问。